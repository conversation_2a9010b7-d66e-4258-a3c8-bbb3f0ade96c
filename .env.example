# Configuration d'environnement pour Odoo 16
# Copiez ce fichier vers .env et modifiez les valeurs selon vos besoins

# Configuration PostgreSQL
POSTGRES_DB=postgres
POSTGRES_USER=odoo
POSTGRES_PASSWORD=odoo

# Configuration Odoo
ODOO_ADMIN_PASSWORD=admin
ODOO_DB_HOST=db
ODOO_DB_PORT=5432
ODOO_DB_USER=odoo
ODOO_DB_PASSWORD=odoo

# Ports d'exposition
ODOO_PORT=8069
ODOO_LONGPOLLING_PORT=8072
POSTGRES_PORT=5432
PGADMIN_PORT=8080

# Configuration pgAdmin
PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin

# Configuration de développement
ODOO_DEV_MODE=reload,qweb,werkzeug,xml
ODOO_LOG_LEVEL=info
ODOO_WORKERS=0

# Limites de ressources
ODOO_LIMIT_MEMORY_HARD=2684354560
ODOO_LIMIT_MEMORY_SOFT=2147483648
ODOO_LIMIT_TIME_CPU=600
ODOO_LIMIT_TIME_REAL=1200
