FROM odoo:16

# Installer des dépendances système supplémentaires
USER root

# Installer des outils de développement et dépendances
RUN apt-get update && apt-get install -y \
    git \
    curl \
    vim \
    nano \
    htop \
    python3-dev \
    python3-pip \
    build-essential \
    libxml2-dev \
    libxslt1-dev \
    libevent-dev \
    libsasl2-dev \
    libldap2-dev \
    libjpeg-dev \
    libpng-dev \
    libfreetype6-dev \
    zlib1g-dev \
    fonts-noto-cjk \
    && rm -rf /var/lib/apt/lists/*

# Installer PostgreSQL client compatible (utiliser la version disponible)
RUN apt-get update && apt-get install -y --allow-downgrades \
    postgresql-client-13 \
    libpq5=13.22-0+deb11u1 \
    libpq-dev=13.22-0+deb11u1 \
    && rm -rf /var/lib/apt/lists/* || \
    (apt-get update && apt-get install -y postgresql-client libpq-dev && rm -rf /var/lib/apt/lists/*)

# Installer des packages Python supplémentaires pour le développement
RUN pip3 install --no-cache-dir \
    debugpy \
    pudb \
    ipython \
    requests \
    beautifulsoup4 \
    lxml \
    openpyxl \
    xlrd \
    xlwt \
    pillow

# Créer les répertoires nécessaires
RUN mkdir -p /mnt/extra-addons \
    && mkdir -p /var/log/odoo \
    && mkdir -p /etc/odoo \
    && chown -R odoo:odoo /mnt/extra-addons \
    && chown -R odoo:odoo /var/log/odoo \
    && chown -R odoo:odoo /etc/odoo

# Revenir à l'utilisateur odoo
USER odoo

# Exposer les ports
EXPOSE 8069 8072

# Point d'entrée par défaut
ENTRYPOINT ["/entrypoint.sh"]
CMD ["odoo"]
