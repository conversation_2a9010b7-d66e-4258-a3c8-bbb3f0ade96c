# Odoo 16 - Configuration Docker Professionnelle

Cette configuration Docker vous permet de démarrer rapidement un environnement de développement Odoo 16 complet avec PostgreSQL et des outils de gestion.

## 🚀 Démarrage rapide

```bash
# Démarrer Odoo 16
./start.sh

# Ou en arrière-plan
./start.sh -d

# Avec pgAdmin inclus
./start.sh -t
```

Accédez à Odoo : **http://localhost:8069**

## 📋 Prérequis

- Docker (version 20.10+)
- Docker Compose (version 2.0+)
- 4 GB de RAM disponible
- 10 GB d'espace disque libre

## 🏗️ Architecture

```
├── docker-compose.yml      # Configuration des services
├── Dockerfile             # Image Odoo personnalisée
├── config/
│   └── odoo.conf          # Configuration Odoo
├── addons/                # Modules personnalisés
├── logs/                  # Logs d'Odoo
└── scripts/               # Scripts de gestion
```

## 🛠️ Services inclus

| Service | Port | Description |
|---------|------|-------------|
| **Odoo** | 8069 | Application principale |
| **PostgreSQL** | 5432 | Base de données |
| **pgAdmin** | 8080 | Interface d'administration DB (optionnel) |

## 📜 Scripts de gestion

### Démarrage
```bash
./start.sh [options]

Options:
  -h, --help          Afficher l'aide
  -b, --build         Reconstruire les images
  -d, --detach        Démarrer en arrière-plan
  -t, --tools         Inclure pgAdmin
  --logs              Afficher les logs après démarrage
  --reset-db          Réinitialiser la base de données
```

### Arrêt
```bash
./stop.sh [options]

Options:
  -h, --help          Afficher l'aide
  -v, --volumes       Supprimer les volumes (⚠️ perte de données)
  --remove-images     Supprimer les images Docker
```

### Logs
```bash
./logs.sh [service] [options]

Services: odoo, db, pgadmin, all
Options:
  -f, --follow        Suivre en temps réel
  -t, --tail N        Dernières N lignes
  --since TIME        Depuis TIME (ex: 2h, 30m)
```

### Redémarrage
```bash
./restart.sh [service] [options]

Services: odoo, db, pgadmin, all
Options:
  -b, --build         Reconstruire avant redémarrage
  -f, --force         Redémarrage forcé
```

## 🔧 Configuration

### Odoo
- **Fichier** : `config/odoo.conf`
- **Mode développement** : Activé par défaut
- **Auto-reload** : Activé pour les modules
- **Logs** : Niveau INFO dans `logs/odoo.log`

### Base de données
- **Utilisateur** : `odoo`
- **Mot de passe** : `odoo`
- **Base** : Créée automatiquement
- **Accès externe** : Port 5432

### pgAdmin (optionnel)
- **URL** : http://localhost:8080
- **Email** : <EMAIL>
- **Mot de passe** : admin

## 📁 Développement

### Ajouter des modules personnalisés
```bash
# Placer vos modules dans le dossier addons/
mkdir -p addons/mon_module
# Redémarrer Odoo pour les détecter
./restart.sh odoo
```

### Accès aux logs
```bash
# Logs en temps réel
./logs.sh -f

# Logs spécifiques
./logs.sh db -t 50
```

### Debugging
```bash
# Mode debug activé par défaut
# Accès au conteneur Odoo
docker exec -it odoo16_app bash

# Accès à PostgreSQL
docker exec -it odoo16_db psql -U odoo
```

## 🔒 Sécurité

### Mots de passe par défaut
⚠️ **Changez ces mots de passe en production !**

- Odoo admin : `admin`
- PostgreSQL : `odoo/odoo`
- pgAdmin : `<EMAIL>/admin`

### Configuration production
```bash
# Modifier config/odoo.conf
workers = 4                    # Activer les workers
admin_passwd = MOT_DE_PASSE_FORT
list_db = False               # Masquer la liste des DB
log_level = warn              # Réduire les logs
```

## 🚨 Dépannage

### Problèmes courants

**Port déjà utilisé**
```bash
# Vérifier les ports occupés
sudo netstat -tlnp | grep :8069
# Arrêter le service conflictuel ou changer le port
```

**Problème de permissions**
```bash
# Réparer les permissions
sudo chown -R $USER:$USER .
chmod +x *.sh
```

**Base de données corrompue**
```bash
# Réinitialiser complètement
./start.sh --reset-db
```

**Conteneur qui ne démarre pas**
```bash
# Vérifier les logs
./logs.sh odoo
# Reconstruire l'image
./start.sh -b
```

### Commandes utiles

```bash
# État des conteneurs
docker ps -a

# Espace disque utilisé
docker system df

# Nettoyer Docker
docker system prune -a

# Sauvegarder la base
docker exec odoo16_db pg_dump -U odoo postgres > backup.sql

# Restaurer la base
docker exec -i odoo16_db psql -U odoo postgres < backup.sql
```

## 📊 Monitoring

### Ressources système
```bash
# Utilisation des conteneurs
docker stats

# Logs système
./logs.sh all --since 1h
```

### Métriques Odoo
- **URL** : http://localhost:8069/web/database/manager
- **Monitoring** : Logs dans `logs/odoo.log`

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature
3. Commit vos changements
4. Push vers la branche
5. Ouvrir une Pull Request

## 📄 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

---

**🎯 Prêt à développer avec Odoo 16 !**

Pour toute question ou problème, consultez la [documentation officielle Odoo](https://www.odoo.com/documentation/16.0/) ou ouvrez une issue.
