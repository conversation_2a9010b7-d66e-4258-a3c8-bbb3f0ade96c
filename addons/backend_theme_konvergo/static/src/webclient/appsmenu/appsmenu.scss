.o_navbar_apps_menu .dropdown-toggle {
	padding: 0px 14px !important;
}
	
.o_navbar_apps_menu.show .dropdown-menu {
	@include konvergo-full-screen-menu();
	top: 51px !important;
	display: flex !important;
	flex-direction: row !important;
	flex-wrap: wrap !important;
	justify-content: flex-start;
    align-content: flex-start;
	background-size: cover;
	overflow: inital;
	@include media-breakpoint-up(lg) {
	    padding: {
	        left: 20vw;
	        right: 20vw;
	    }
	}
	.o_app {
		transition: transform .2s cubic-bezier(.32,.08,.24,1);

		margin-top: 20px;
	    width: percentage(1/3);
	    @include media-breakpoint-up(sm) {
	        width: percentage(1/4);
	    }
	    @include media-breakpoint-up(md) {
	        width: percentage(1/6);
	    }
	   	> a {
		    display: flex;
		    align-items: center;
		    flex-direction: column;
		    justify-content: flex-start;
	   		.konvergo_app_icon {
			    height: auto;
			    max-width: 7rem;
			    width: 100%;
			    flex-basis: 0;
				border-radius: 22px;
			}
			.konvergo_app_name {
				color: $o-brand-odoo; 
				margin-top: 17px;
			}
	   	}
	    &.focus, &:hover, &:focus {

			transform: scale3d(1.12,1.12,1.12);
			transition: transform $konvergo-transition-style;
			background: none;
			> a {
				background: none;
			}
			// .konvergo_app_icon {
			//     transform: translateY(-1px);
			// }
	    }

		&.active, &:active {
			opacity: 0.7;
			transform: scale3d(1.0,1.0,1.0);
			transition: transform $konvergo-transition-style;
		}
	}
}

.o_main_navbar .o_menu_brand.hide-menu-brand {
    display: none !important;
}
