[options]
# Configuration de base
addons_path = /usr/lib/python3/dist-packages/odoo/addons,/mnt/extra-addons
data_dir = /var/lib/odoo

# Configuration de la base de données
db_host = db
db_port = 5432
db_user = odoo
db_password = odoo
db_name = False
db_template = template0

# Configuration du serveur
xmlrpc = True
xmlrpc_interface = 0.0.0.0
xmlrpc_port = 8069
longpolling_port = 8072

# Configuration des logs
log_level = info
log_handler = :INFO
logfile = /var/log/odoo/odoo.log
log_db = False

# Configuration pour le développement
dev_mode = reload,qweb,werkzeug,xml
list_db = True
admin_passwd = admin

# Configuration des workers (pour la production, mettre à 0 pour le dev)
workers = 0
max_cron_threads = 1

# Limites
limit_memory_hard = 2684354560
limit_memory_soft = 2147483648
limit_request = 8192
limit_time_cpu = 600
limit_time_real = 1200
limit_time_real_cron = -1

# Configuration des emails (optionnel)
# smtp_server = localhost
# smtp_port = 587
# smtp_user = False
# smtp_password = False
# smtp_ssl = False

# Configuration de sécurité
# proxy_mode = True
# dbfilter = ^%d$

# Configuration des sessions
# session_dir = /tmp

# Configuration des rapports
# reportgz = False

# Configuration du cache
# enable_redis = False
# redis_host = localhost
# redis_port = 6379
# redis_dbindex = 1
# redis_pass = False
