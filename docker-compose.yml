services:
  db:
    image: postgres:15
    container_name: odoo16_db
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - odoo-db-data:/var/lib/postgresql/data/pgdata
    ports:
      - "5432:5432"
    networks:
      - odoo-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U odoo"]
      interval: 30s
      timeout: 10s
      retries: 5

  odoo:
    build: .
    container_name: odoo16_app
    depends_on:
      db:
        condition: service_healthy
    ports:
      - "8069:8069"
      - "8072:8072"  # Pour le longpolling
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./addons:/mnt/extra-addons
      - ./logs:/var/log/odoo
    environment:
      - HOST=db
      - USER=odoo
      - PASSWORD=odoo
    networks:
      - odoo-network
    restart: unless-stopped
    command: odoo --config=/etc/odoo/odoo.conf --dev=reload,qweb,werkzeug,xml

  # Service optionnel pour pgAdmin
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: odoo16_pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin-data:/var/lib/pgadmin
    ports:
      - "8080:80"
    networks:
      - odoo-network
    restart: unless-stopped
    profiles:
      - tools

volumes:
  odoo-web-data:
  odoo-db-data:
  pgadmin-data:

networks:
  odoo-network:
    driver: bridge
