#!/bin/bash

# Script pour afficher les logs d'Odoo 16
# Usage: ./logs.sh [service] [options]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction d'aide
show_help() {
    echo "Usage: $0 [SERVICE] [OPTIONS]"
    echo ""
    echo "Services disponibles:"
    echo "  odoo                Logs du service Odoo (défaut)"
    echo "  db                  Logs de PostgreSQL"
    echo "  pgadmin             Logs de pgAdmin"
    echo "  all                 Logs de tous les services"
    echo ""
    echo "Options:"
    echo "  -h, --help          Afficher cette aide"
    echo "  -f, --follow        Suivre les logs en temps réel"
    echo "  -t, --tail N        Afficher les N dernières lignes (défaut: 100)"
    echo "  --since TIME        Afficher les logs depuis TIME (ex: 2h, 30m)"
    echo ""
    echo "Exemples:"
    echo "  $0                  Afficher les logs d'Odoo"
    echo "  $0 -f               Suivre les logs d'Odoo en temps réel"
    echo "  $0 db -t 50         Afficher les 50 dernières lignes de PostgreSQL"
    echo "  $0 all --since 1h   Afficher tous les logs depuis 1 heure"
}

# Variables par défaut
SERVICE="odoo"
FOLLOW=false
TAIL="100"
SINCE=""

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--follow)
            FOLLOW=true
            shift
            ;;
        -t|--tail)
            TAIL="$2"
            shift 2
            ;;
        --since)
            SINCE="$2"
            shift 2
            ;;
        odoo|db|pgadmin|all)
            SERVICE="$1"
            shift
            ;;
        *)
            log_error "Option ou service inconnu: $1"
            show_help
            exit 1
            ;;
    esac
done

# Utiliser docker compose ou docker-compose selon la disponibilité
COMPOSE_CMD="docker compose"
if ! docker compose version &> /dev/null; then
    COMPOSE_CMD="docker-compose"
fi

# Construire les options
OPTIONS="--tail=$TAIL"

if [ "$FOLLOW" = true ]; then
    OPTIONS="$OPTIONS -f"
fi

if [ -n "$SINCE" ]; then
    OPTIONS="$OPTIONS --since=$SINCE"
fi

# Afficher les logs
case $SERVICE in
    "all")
        log_info "Affichage des logs de tous les services..."
        $COMPOSE_CMD logs $OPTIONS
        ;;
    "odoo")
        log_info "Affichage des logs d'Odoo..."
        $COMPOSE_CMD logs $OPTIONS odoo
        ;;
    "db")
        log_info "Affichage des logs de PostgreSQL..."
        $COMPOSE_CMD logs $OPTIONS db
        ;;
    "pgadmin")
        log_info "Affichage des logs de pgAdmin..."
        $COMPOSE_CMD logs $OPTIONS pgadmin
        ;;
    *)
        log_error "Service inconnu: $SERVICE"
        show_help
        exit 1
        ;;
esac
