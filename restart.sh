#!/bin/bash

# Script de redémarrage pour Odoo 16 avec Docker
# Usage: ./restart.sh [options]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction d'aide
show_help() {
    echo "Usage: $0 [SERVICE] [OPTIONS]"
    echo ""
    echo "Services:"
    echo "  odoo                Redémarrer seulement Odoo (défaut)"
    echo "  db                  Redémarrer seulement PostgreSQL"
    echo "  pgadmin             Redémarrer seulement pgAdmin"
    echo "  all                 Redémarrer tous les services"
    echo ""
    echo "Options:"
    echo "  -h, --help          Afficher cette aide"
    echo "  -b, --build         Reconstruire les images avant redémarrage"
    echo "  -f, --force         Forcer le redémarrage (kill + start)"
    echo ""
    echo "Exemples:"
    echo "  $0                  Redémarrer Odoo"
    echo "  $0 all -b           Reconstruire et redémarrer tous les services"
    echo "  $0 odoo -f          Forcer le redémarrage d'Odoo"
}

# Variables par défaut
SERVICE="odoo"
BUILD=false
FORCE=false

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -f|--force)
            FORCE=true
            shift
            ;;
        odoo|db|pgadmin|all)
            SERVICE="$1"
            shift
            ;;
        *)
            log_error "Option ou service inconnu: $1"
            show_help
            exit 1
            ;;
    esac
done

# Utiliser docker compose ou docker-compose selon la disponibilité
COMPOSE_CMD="docker compose"
if ! docker compose version &> /dev/null; then
    COMPOSE_CMD="docker-compose"
fi

log_info "Redémarrage de $SERVICE..."

# Reconstruire si demandé
if [ "$BUILD" = true ]; then
    log_info "Reconstruction des images..."
    if [ "$SERVICE" = "all" ]; then
        $COMPOSE_CMD build --no-cache
    else
        $COMPOSE_CMD build --no-cache $SERVICE
    fi
fi

# Redémarrer le service
if [ "$FORCE" = true ]; then
    log_warning "Redémarrage forcé..."
    if [ "$SERVICE" = "all" ]; then
        $COMPOSE_CMD kill
        $COMPOSE_CMD up -d
    else
        $COMPOSE_CMD kill $SERVICE
        $COMPOSE_CMD up -d $SERVICE
    fi
else
    log_info "Redémarrage normal..."
    $COMPOSE_CMD restart $SERVICE
fi

log_success "Redémarrage de $SERVICE terminé !"

# Afficher l'état des services
echo ""
echo "📊 État des services:"
$COMPOSE_CMD ps
