#!/bin/bash

# Script de démarrage pour Odoo 16 avec Docker
# Usage: ./start.sh [options]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction d'aide
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Afficher cette aide"
    echo "  -b, --build         Forcer la reconstruction des images"
    echo "  -d, --detach        Démarrer en arrière-plan"
    echo "  -t, --tools         Inclure pgAdmin"
    echo "  --logs              Afficher les logs après démarrage"
    echo "  --reset-db          Réinitialiser la base de données"
    echo ""
    echo "Exemples:"
    echo "  $0                  Démarrer Odoo normalement"
    echo "  $0 -b -d            Reconstruire et démarrer en arrière-plan"
    echo "  $0 -t               Démarrer avec pgAdmin"
}

# Variables par défaut
BUILD=false
DETACH=false
TOOLS=false
SHOW_LOGS=false
RESET_DB=false

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -b|--build)
            BUILD=true
            shift
            ;;
        -d|--detach)
            DETACH=true
            shift
            ;;
        -t|--tools)
            TOOLS=true
            shift
            ;;
        --logs)
            SHOW_LOGS=true
            shift
            ;;
        --reset-db)
            RESET_DB=true
            shift
            ;;
        *)
            log_error "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Vérifier que Docker est installé et en cours d'exécution
if ! command -v docker &> /dev/null; then
    log_error "Docker n'est pas installé"
    exit 1
fi

if ! docker info &> /dev/null; then
    log_error "Docker n'est pas en cours d'exécution"
    exit 1
fi

# Vérifier que docker-compose est disponible
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    log_error "Docker Compose n'est pas disponible"
    exit 1
fi

# Utiliser docker compose ou docker-compose selon la disponibilité
COMPOSE_CMD="docker compose"
if ! docker compose version &> /dev/null; then
    COMPOSE_CMD="docker-compose"
fi

log_info "Démarrage d'Odoo 16 avec Docker..."

# Réinitialiser la base de données si demandé
if [ "$RESET_DB" = true ]; then
    log_warning "Réinitialisation de la base de données..."
    $COMPOSE_CMD down -v
    docker volume rm odoo_odoo-db-data 2>/dev/null || true
fi

# Construire les images si nécessaire
if [ "$BUILD" = true ]; then
    log_info "Construction des images Docker..."
    $COMPOSE_CMD build --no-cache
fi

# Préparer les options de démarrage
COMPOSE_OPTIONS=""
if [ "$DETACH" = true ]; then
    COMPOSE_OPTIONS="$COMPOSE_OPTIONS -d"
fi

# Préparer les profils
PROFILES=""
if [ "$TOOLS" = true ]; then
    PROFILES="--profile tools"
fi

# Démarrer les services
log_info "Démarrage des conteneurs..."
$COMPOSE_CMD up $PROFILES $COMPOSE_OPTIONS

# Afficher les informations de connexion
if [ "$DETACH" = true ]; then
    log_success "Odoo 16 démarré avec succès !"
    echo ""
    echo "🌐 Accès à Odoo: http://localhost:8069"
    echo "📊 Base de données: PostgreSQL sur localhost:5432"
    if [ "$TOOLS" = true ]; then
        echo "🔧 pgAdmin: http://localhost:8080 (<EMAIL> / admin)"
    fi
    echo ""
    echo "📋 Commandes utiles:"
    echo "  ./logs.sh           - Voir les logs"
    echo "  ./stop.sh           - Arrêter les services"
    echo "  ./restart.sh        - Redémarrer les services"
    echo ""
    
    if [ "$SHOW_LOGS" = true ]; then
        log_info "Affichage des logs..."
        $COMPOSE_CMD logs -f
    fi
fi
