#!/bin/bash

# Script d'arrêt pour Odoo 16 avec Docker
# Usage: ./stop.sh [options]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction d'aide
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Options:"
    echo "  -h, --help          Afficher cette aide"
    echo "  -v, --volumes       Supprimer aussi les volumes (données perdues !)"
    echo "  --remove-images     Supprimer aussi les images Docker"
    echo ""
    echo "Exemples:"
    echo "  $0                  Arrêter les conteneurs"
    echo "  $0 -v               Arrêter et supprimer les volumes"
}

# Variables par défaut
REMOVE_VOLUMES=false
REMOVE_IMAGES=false

# Traitement des arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -v|--volumes)
            REMOVE_VOLUMES=true
            shift
            ;;
        --remove-images)
            REMOVE_IMAGES=true
            shift
            ;;
        *)
            log_error "Option inconnue: $1"
            show_help
            exit 1
            ;;
    esac
done

# Utiliser docker compose ou docker-compose selon la disponibilité
COMPOSE_CMD="docker compose"
if ! docker compose version &> /dev/null; then
    COMPOSE_CMD="docker-compose"
fi

log_info "Arrêt d'Odoo 16..."

# Arrêter les conteneurs
if [ "$REMOVE_VOLUMES" = true ]; then
    log_warning "Arrêt des conteneurs et suppression des volumes..."
    $COMPOSE_CMD down -v --remove-orphans
else
    log_info "Arrêt des conteneurs..."
    $COMPOSE_CMD down --remove-orphans
fi

# Supprimer les images si demandé
if [ "$REMOVE_IMAGES" = true ]; then
    log_warning "Suppression des images Docker..."
    $COMPOSE_CMD down --rmi all
fi

log_success "Odoo 16 arrêté avec succès !"

# Afficher l'état des conteneurs
echo ""
echo "📊 État des conteneurs:"
docker ps -a --filter "name=odoo16" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
