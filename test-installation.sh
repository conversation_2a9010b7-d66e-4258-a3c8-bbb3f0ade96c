#!/bin/bash

# Script de test pour vérifier l'installation Odoo 16
# Usage: ./test-installation.sh

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Fonction de test
test_service() {
    local service_name=$1
    local url=$2
    local expected_code=$3
    
    log_info "Test de $service_name..."
    
    local response_code=$(curl -s -o /dev/null -w "%{http_code}" "$url" || echo "000")
    
    if [ "$response_code" = "$expected_code" ]; then
        log_success "$service_name fonctionne correctement (code: $response_code)"
        return 0
    else
        log_error "$service_name ne répond pas correctement (code: $response_code, attendu: $expected_code)"
        return 1
    fi
}

# Utiliser docker compose ou docker-compose selon la disponibilité
COMPOSE_CMD="docker compose"
if ! docker compose version &> /dev/null; then
    COMPOSE_CMD="docker-compose"
fi

echo "🧪 Test de l'installation Odoo 16"
echo "=================================="
echo ""

# Test 1: Vérifier que les conteneurs sont en cours d'exécution
log_info "Vérification de l'état des conteneurs..."
if $COMPOSE_CMD ps | grep -q "Up"; then
    log_success "Les conteneurs sont en cours d'exécution"
else
    log_error "Les conteneurs ne sont pas en cours d'exécution"
    echo ""
    echo "État des conteneurs:"
    $COMPOSE_CMD ps
    exit 1
fi

# Test 2: Vérifier la connectivité Odoo
test_service "Odoo" "http://localhost:8069" "303"

# Test 3: Vérifier la connectivité PostgreSQL
log_info "Test de PostgreSQL..."
if docker exec odoo16_db pg_isready -U odoo > /dev/null 2>&1; then
    log_success "PostgreSQL fonctionne correctement"
else
    log_error "PostgreSQL ne répond pas"
fi

# Test 4: Vérifier les volumes
log_info "Vérification des volumes..."
volumes=$(docker volume ls --filter name=odoo --format "{{.Name}}" | wc -l)
if [ "$volumes" -ge 2 ]; then
    log_success "Les volumes sont créés ($volumes volumes trouvés)"
else
    log_warning "Nombre de volumes insuffisant ($volumes volumes trouvés)"
fi

# Test 5: Vérifier les logs
log_info "Vérification des logs Odoo..."
if $COMPOSE_CMD logs odoo | grep -q "odoo.modules.loading"; then
    log_success "Odoo se charge correctement"
else
    log_warning "Odoo pourrait avoir des problèmes de chargement"
fi

# Test 6: Vérifier l'accès aux fichiers de configuration
log_info "Vérification des fichiers de configuration..."
if [ -f "config/odoo.conf" ]; then
    log_success "Fichier de configuration trouvé"
else
    log_error "Fichier de configuration manquant"
fi

# Test 7: Vérifier l'espace disque
log_info "Vérification de l'espace disque utilisé par Docker..."
docker_size=$(docker system df --format "table {{.Type}}\t{{.TotalCount}}\t{{.Size}}" | grep -E "(Images|Containers|Local Volumes)" | awk '{sum += $3} END {print sum}' || echo "0")
log_info "Espace utilisé par Docker: $(docker system df -v | grep 'Total' | awk '{print $4}' || echo 'N/A')"

echo ""
echo "🎯 Résumé des tests"
echo "==================="

# Afficher les informations de connexion
echo ""
echo "📋 Informations de connexion:"
echo "  🌐 Odoo: http://localhost:8069"
echo "  📊 PostgreSQL: localhost:5432 (utilisateur: odoo, mot de passe: odoo)"
echo ""

# Afficher les commandes utiles
echo "🛠️  Commandes utiles:"
echo "  ./logs.sh           - Voir les logs"
echo "  ./stop.sh           - Arrêter les services"
echo "  ./restart.sh        - Redémarrer les services"
echo "  ./start.sh -t       - Démarrer avec pgAdmin"
echo ""

log_success "Tests terminés ! Odoo 16 est prêt à être utilisé."
echo ""
echo "💡 Pour commencer:"
echo "   1. Ouvrez http://localhost:8069 dans votre navigateur"
echo "   2. Créez votre première base de données"
echo "   3. Commencez à développer vos modules dans le dossier 'addons/'"
